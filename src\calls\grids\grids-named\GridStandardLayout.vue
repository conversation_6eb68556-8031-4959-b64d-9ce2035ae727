<template>
  <div style="height: 100%">
    <div class="grid--content-pane">
      <splitpanes
        class="default-theme"
        v-on:ready="paneReady"
        v-on:pane-click="paneClick"
        v-on:resized="paneResized"
        horizontal
      >
        <pane
          key="grid"
          :size="configStoreState.splitPanes.gridContentDefault.size"
          :id="'grid-standard-layout' + SUFFIX"
          style="overflow: hidden; display: flex; flex-direction: column;"
        >
          <GridStandard2
            :id="SUFFIX"
            v-if="isReady"
            :grid-definition="gridDefinition"
            v-on:onRowClicked="onRowClicked"
            v-on:onRowRightClick="onRowRightClick"
            v-on:loadingSelectedCallPerms="setIsSelectedCallPermsLoading"
            v-on:selectedCallPerms="setSelectedCallPerms"
            style="flex: 1; min-height: 0;"
          />
        </pane>

        <pane
          key="summary"
          :size="configStoreState.splitPanes.callSummaryDefault.size"
          :min-size="configStoreState.splitPanes.callSummaryDefault.minSize"
          :max-size="configStoreState.splitPanes.callSummaryDefault.maxSize"
        >
          <CallSummarySection
            :cleo-call-summary="cleoCallSummary"
            :is-loading-perms="isLoadingPermsForCall"
            :cleo-call-summary-perms="permsForSelectedCall"
          />
        </pane>
      </splitpanes>

      <CleoContextMenu2
        :show-menu="rightClickController.showRightClickMenu.value"
        :contain-with-dom-id="'grid-standard-layout' + SUFFIX"
        :perms-loading="isLoadingPermsForCall"
        :perms-for-call="permsForSelectedCall"
        :cleo-call-summary="rightClickController.cleoCallSummary.value"
        :clicked-element-coords="rightClickController.rightClickCoords.value"
        v-on:hideMenu="rightClickController.showRightClickMenu.value = false"
      />

      <!--      <CleoModal header-message="Location Finder" v-if="state.address.showModal">-->
      <!--        <div slot="body">-->
      <!--          <What3WordsCleo-->
      <!--            :what3words-controller-state="what3wordsControllerState"-->
      <!--            :call-detail="state.callDetail"-->
      <!--            :user-permissions="userPermissions"-->
      <!--            v-on:close="state.address.showModal = false"-->
      <!--            v-on:confirmed="what3wordsMapConfirmed"-->
      <!--          />-->
      <!--        </div>-->
      <!--        <div slot="buttons"></div>-->
      <!--      </CleoModal>-->
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType, ref } from "@vue/composition-api";
import { IGridDefinition } from "../grid-models";
import GridStandard2 from "@/calls/grids/grids-named/GridStandard2.vue";
import CallSummarySection from "@/calls/summary/CallSummarySection.vue";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CommonService } from "@/common/common-service";

import Splitpanes from "../../../../node_modules/splitpanes/src/components/splitpanes/splitpanes.vue";
import Pane from "../../../../node_modules/splitpanes/src/components/splitpanes/pane.vue";

import { appStore } from "@/store/store";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
  ISplitPane,
} from "@/common/config/config-store";
import { loggerInstance } from "@/common/Logger";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import CleoContextMenu2 from "@/calls/grids/contextmenu/cleo-context-menu2.vue";
import { ICleoPermission } from "@/permissions/permission-models";
import { useRightClickController } from "@/calls/grids/contextmenu/useRightClickController";

const callSummaryService = new CallSummaryService();
const commonService = new CommonService();

export default defineComponent({
  name: "grid-standard-layout",
  components: {
    CleoContextMenu2,
    CallSummarySection,
    GridStandard2,
    Splitpanes,
    Pane,
  },
  props: {
    gridDefinition: {
      type: Object as PropType<IGridDefinition>,
      required: true,
    },
  },
  setup(props: { gridDefinition: IGridDefinition }) {
    const store = appStore;

    const SUFFIX = Math.random()
      .toString(36)
      .substring(2);

    const configStoreState = computed<IConfigStoreState>(() => {
      return store.state[CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME];
    });

    const userStoreState = computed<IUserStoreState>(() => {
      return store.state[USER_STORE_CONST.USER__CONST_MODULE_NAME];
    });

    const cleoCallSummary = ref<ICleoCallSummary>(
      callSummaryService.factoryCleoCallSummary()
    );

    const rightClickController = useRightClickController();

    function onRowRightClick(payload: {
      data: ICleoCallSummary;
      coords: { x: number; y: number };
      targetGridContainerId: string;
    }) {
      rightClickController.startMenu(payload);

      onRowClicked(payload.data);
    }

    function onRowClicked(cleoCall: ICleoCallSummary) {
      permsForSelectedCall.value = {};
      cleoCallSummary.value = commonService.simpleObjectClone(cleoCall);
    }

    const isLoadingPermsForCall = ref(false);
    const permsForSelectedCall = ref({});
    function setIsSelectedCallPermsLoading(isLoading: boolean): void {
      isLoadingPermsForCall.value = isLoading;
    }

    function setSelectedCallPerms(
      perms: Record<string, ICleoPermission>
    ): void {
      permsForSelectedCall.value = commonService.simpleObjectClone(perms);
    }

    function paneReady(): void {
      console.log("...paneReady");
    }

    function paneResized(panes: ISplitPane[]): void {
      console.log("...paneResized");
      store.commit(
        CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME +
          "/" +
          CONFIG_STORE_CONST.CONFIG__MUTATION_SET_SPLIT_PANES,
        panes
      );
    }

    function paneClick(a: any): void {
      loggerInstance.log("...paneClickSummary");
    }

    const isReady = computed(() => {
      return userStoreState.value.user.userName.length > 0;
    });

    return {
      SUFFIX,
      configStoreState,
      cleoCallSummary,
      onRowClicked,
      paneReady,
      paneResized,
      paneClick,
      isLoadingPermsForCall,
      permsForSelectedCall,
      onRowRightClick,
      setIsSelectedCallPermsLoading,
      setSelectedCallPerms,
      isReady,
      rightClickController,
    };
  },
});
</script>
